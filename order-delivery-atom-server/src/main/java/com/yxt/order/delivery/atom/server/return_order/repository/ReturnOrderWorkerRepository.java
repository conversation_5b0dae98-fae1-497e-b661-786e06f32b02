package com.yxt.order.delivery.atom.server.return_order.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.delivery.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.delivery.atom.common.sharding.YxtOrderSharding;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderWorkerDO;
import com.yxt.order.delivery.atom.server.return_order.mapper.ReturnOrderWorkerMapper;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class ReturnOrderWorkerRepository extends ServiceImpl<ReturnOrderWorkerMapper, ReturnOrderWorkerDO> {

  @YxtOrderSharding(shardingNo = "#returnNo")
  public List<ReturnOrderWorkerDO> getReturnOrderWorkerList(String returnNo) {
    return this.list(Wrappers.<ReturnOrderWorkerDO>lambdaQuery().eq(ReturnOrderWorkerDO::getReturnNo, returnNo));
  }

}
