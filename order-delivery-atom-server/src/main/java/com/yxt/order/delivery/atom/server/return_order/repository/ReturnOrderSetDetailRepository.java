package com.yxt.order.delivery.atom.server.return_order.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.delivery.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.delivery.atom.common.sharding.YxtOrderSharding;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderSetDetailDO;
import com.yxt.order.delivery.atom.server.return_order.mapper.ReturnOrderSetDetailMapper;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class ReturnOrderSetDetailRepository extends ServiceImpl<ReturnOrderSetDetailMapper, ReturnOrderSetDetailDO> {

  @YxtOrderSharding(shardingNo = "#returnNo")
  public List<ReturnOrderSetDetailDO> getReturnOrderSetDetailList(String returnNo) {
    return this.list(Wrappers.<ReturnOrderSetDetailDO>lambdaQuery().eq(ReturnOrderSetDetailDO::getReturnNo, returnNo));
  }

}
