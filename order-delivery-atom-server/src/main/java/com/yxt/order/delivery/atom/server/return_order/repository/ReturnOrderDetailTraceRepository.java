package com.yxt.order.delivery.atom.server.return_order.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.delivery.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.delivery.atom.common.sharding.YxtOrderSharding;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderDetailTraceDO;
import com.yxt.order.delivery.atom.server.return_order.mapper.ReturnOrderDetailTraceMapper;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class ReturnOrderDetailTraceRepository extends ServiceImpl<ReturnOrderDetailTraceMapper, ReturnOrderDetailTraceDO> {

  @YxtOrderSharding(shardingNo = "#returnNo")
  public List<ReturnOrderDetailTraceDO> getReturnOrderDetailTraceList(String returnNo) {
    return this.list(Wrappers.<ReturnOrderDetailTraceDO>lambdaQuery().eq(ReturnOrderDetailTraceDO::getReturnNo, returnNo));
  }

}
