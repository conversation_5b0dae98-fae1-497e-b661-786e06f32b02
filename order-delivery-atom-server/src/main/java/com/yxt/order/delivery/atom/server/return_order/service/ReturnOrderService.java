package com.yxt.order.delivery.atom.server.return_order.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.exception.YxtParamException;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.common.utils.CompletableFutureUtils;
import com.yxt.order.delivery.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.delivery.atom.common.configration.ThreadPoolConfig;
import com.yxt.order.delivery.atom.common.sharding.Hit;
import com.yxt.order.delivery.atom.common.sharding.TableShardingHintAlgorithm;
import com.yxt.order.delivery.atom.sdk.return_order.common.ReturnOrderDTO;
import com.yxt.order.delivery.atom.sdk.return_order.common.ReturnOrderDetailDTO;
import com.yxt.order.delivery.atom.sdk.return_order.common.ReturnOrderDetailPickDTO;
import com.yxt.order.delivery.atom.sdk.return_order.common.ReturnOrderDetailTraceDTO;
import com.yxt.order.delivery.atom.sdk.return_order.common.ReturnOrderSetDetailDTO;
import com.yxt.order.delivery.atom.sdk.return_order.req.ReturnBatchQueryByScaleReq;
import com.yxt.order.delivery.atom.sdk.return_order.req.ReturnQueryByScaleReq;
import com.yxt.order.delivery.atom.sdk.return_order.req.SaveReturnOrderOptionalReq;
import com.yxt.order.delivery.atom.sdk.return_order.req.UpdateReturnOrderOptionalReq;
import com.yxt.order.delivery.atom.sdk.return_order.res.ReturnRelatedInfoRes;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderDO;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderDetailDO;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderDetailPickDO;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderDetailTraceDO;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderSetDetailDO;
import com.yxt.order.delivery.atom.server.return_order.entity.ReturnOrderWorkerDO;
import com.yxt.order.delivery.atom.server.return_order.mapper.ReturnOrderMapper;
import com.yxt.order.delivery.atom.server.return_order.repository.ReturnOrderDetailBatchRepository;
import com.yxt.order.delivery.atom.server.return_order.repository.ReturnOrderDetailPickRepository;
import com.yxt.order.delivery.atom.server.return_order.repository.ReturnOrderDetailTraceRepository;
import com.yxt.order.delivery.atom.server.return_order.repository.ReturnOrderSetDetailRepository;
import com.yxt.order.delivery.atom.server.return_order.repository.ReturnOrderWorkerRepository;
import com.yxt.order.types.order_world.ReturnQueryScaleEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Service
public class ReturnOrderService {

  @Autowired
  private ReturnOrderMapper returnOrderMapper;
  @Autowired
  private ReturnOrderDetailBatchRepository returnOrderDetailBatchRepository;
  @Autowired
  private ReturnSearchService returnSearchService;
  
  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_POOL)
  private Executor orderSearchPool;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_SUB_POOL)
  private Executor orderSearchSubPool;
  

  @Transactional
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void saveOptional(SaveReturnOrderOptionalReq request) {
    ReturnOrderDTO returnOrder = request.getReturnOrder();
    try (HintManager hintManager = HintManager.getInstance()) {
      Hit hit = new Hit();
      hit.setDefineNo(returnOrder.getReturnNo());
      TableShardingHintAlgorithm.setHintManager(hintManager, hit);

      //退货单保存
      returnOrderMapper.insert(BeanUtil.copyProperties(returnOrder, ReturnOrderDO.class));

      if (CollUtil.isNotEmpty(request.getReturnOrderDetailList())) {

        returnOrderDetailBatchRepository.saveBatch(BeanUtil.copyToList(request.getReturnOrderDetailList(), ReturnOrderDetailDO.class));
      }
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void updateOptional(UpdateReturnOrderOptionalReq req) {
    try (HintManager hintManager = HintManager.getInstance()) {
      Hit hit = new Hit();
      hit.setDefineNo(req.getReturnNo());
      TableShardingHintAlgorithm.setHintManager(hintManager, hit);

      if(ObjectUtil.isNotEmpty(req.getReturnOrder())){
        ReturnOrderDO originReturnOrder = returnOrderMapper.selectOne(new LambdaQueryWrapper<ReturnOrderDO>().eq(ReturnOrderDO::getReturnNo, req.getReturnNo()));
        if(ObjectUtil.isNull(originReturnOrder)){
          throw new YxtParamException("退货单不存在");
        }
        ReturnOrderDO updateReturnOrder = BeanUtil.copyProperties(req.getReturnOrder(), ReturnOrderDO.class);
        updateReturnOrder.setId(originReturnOrder.getId());
        returnOrderMapper.updateById(updateReturnOrder);
      }
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public ReturnRelatedInfoRes getReturnInfoByScale(ReturnQueryByScaleReq request) {
    String returnNo = request.getReturnNo();
    AtomicReference<ReturnRelatedInfoRes> resDto = new AtomicReference<>(new ReturnRelatedInfoRes());
    ReturnOrderDO returnOrderDO = null;
    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    try (HintManager hintManager = HintManager.getInstance()) {
      Hit hit = new Hit();
      hit.setDefineNo(returnNo);
      TableShardingHintAlgorithm.setHintManager(hintManager, hit);
      // 查询主单
      returnOrderDO = returnOrderMapper.selectOne(new LambdaQueryWrapper<ReturnOrderDO>().eq(ReturnOrderDO::getReturnNo, returnNo));

      Assert.isTrue(Objects.nonNull(returnOrderDO), String.format("退货单%s不存在,req:%s", returnNo, JsonUtils.toJson(request)));
    }
    for (ReturnQueryScaleEnum qryScaleEnum : request.getQryScaleList()) {
      switch (qryScaleEnum) {
        case MAIN:
          ReturnOrderDTO returnOrderDTO = BeanUtil.toBean(returnOrderDO, ReturnOrderDTO.class);
          resDto.get().setReturnOrder(returnOrderDTO);
          break;
        case DETAIL:
          CompletableFuture<Void> detailFuture = CompletableFuture.runAsync(() -> {
            List<ReturnOrderDetailDO> refundDetailList = returnSearchService.getReturnDetailList(returnNo);
            List<ReturnOrderDetailDTO> returnOrderDetailDTOS = BeanUtil.copyToList(refundDetailList, ReturnOrderDetailDTO.class);
            resDto.get().setReturnOrderDetailList(returnOrderDetailDTOS);
          }, orderSearchSubPool);
          futureList.add(detailFuture);
          break;
        case DETAIL_PICK:
          CompletableFuture<Void> detailPickFuture = CompletableFuture.runAsync(() -> {

          }, orderSearchSubPool);
          futureList.add(detailPickFuture);
          break;
        case DETAIL_TRACE:
          CompletableFuture<Void> detailTraceFuture = CompletableFuture.runAsync(() -> {

          }, orderSearchSubPool);
          futureList.add(detailTraceFuture);
          break;
        case WORKER:
          CompletableFuture<Void> workerFuture = CompletableFuture.runAsync(() -> {

          }, orderSearchSubPool);
          futureList.add(workerFuture);
          break;
        case SET_DETAIL:
          CompletableFuture<Void> setDetailFuture = CompletableFuture.runAsync(() -> {

          }, orderSearchSubPool);
          futureList.add(setDetailFuture);
          break;
        default:
          break;
      }
    }
    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    return resDto.get();
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<ReturnRelatedInfoRes> getReturnInfoBatchByScale(ReturnBatchQueryByScaleReq request) {
    Function<String, Supplier<ReturnRelatedInfoRes>> function = (refundNo) -> () -> {
      ReturnQueryByScaleReq tempRequest = new ReturnQueryByScaleReq(request.getQryScaleList(), refundNo);
      return SpringUtil.getBean(ReturnOrderService.class).getReturnInfoByScale(tempRequest);
    };
    return CompletableFutureUtils.supplyAsync(function, request.getReturnNoList(), 10, orderSearchPool).stream().filter(Objects::nonNull).collect(Collectors.toList());

  }
}
